import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import db from "@/lib/db";
import { formatCurrency, formatNumber } from "@/lib/formaters";


	async function getSales() {
		const data = await db.order.aggregate({
			_sum: {
				pricePaidInCents: true,
			},
			_count: true
		})

		return { amount: (data._sum.pricePaidInCents || 0) / 100, numberOfSales: data._count }
	}

	async function getUsers (){

		const [ numberOfUsers, orderData ] = await Promise.all([
			db.user.count(), 
			db.order.aggregate({
				_sum : {
					pricePaidInCents: true,
				}
		})])

		return {
			numberOfUsers,
			averageValuePerUser: numberOfUsers === 0 ? 0:  (orderData._sum.pricePaidInCents || 0) / numberOfUsers / 100
		}
		
	}

	async function getProducts() {

		const [ activeProducts, inactiveProducts ] = await Promise.all([
			db.product.count({
				where: {
					isAvailableForPurchase: true
				}
			}),
			db.product.count({
				where: {
					isAvailableForPurchase: false
				}
			})
		])

		return { activeProducts, inactiveProducts }
	}

export default async function Page() {

const [ sales, users, products ] = await Promise.all([getSales(), getUsers(), getProducts()])

  return (
		<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3'>
			<DashboardCard title='Products' description={`${products.inactiveProducts} inactive products`} text={formatNumber(products.activeProducts)} />
			<DashboardCard
				title='Users'
				description={`average price paid per user: ${formatCurrency(users.averageValuePerUser)}`}
				text={formatNumber(users.numberOfUsers)}
			/>
			<DashboardCard
				title='Orders'
				description={`Total number of sales ${formatNumber(sales.numberOfSales)}`}
				text={formatCurrency(sales.amount)}
			/>
		</div>
	)
}

type DashboardCardProps = {
    title: string;
    description: string;
    text: string;
}

function DashboardCard({title, description, text}:DashboardCardProps){

    return (
			<Card>
				<CardHeader>
					<CardTitle>{title}</CardTitle>
					<CardDescription>{description}</CardDescription>
				</CardHeader>
				<CardContent>
					<p>{text}</p>
				</CardContent>
			</Card>
		)
}
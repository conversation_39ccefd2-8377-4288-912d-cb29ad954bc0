"use server"

import db from "@/lib/db"
import z from "zod"
import fs from "fs/promises"
import { redirect } from "next/navigation"

const fileSchema = z.instanceof(File, { message: "required" })

const imageSchema = fileSchema.refine((file) => file.size === 0 || file.type.startsWith("image/"), "required")
const addSchema = z.object({
	name: z.string().min(1),
	priceInCents: z.coerce.number().int().min(1),
	description: z.string().min(1),
	file: fileSchema.refine((file) => file.size > 0, "required"),
	image: imageSchema.refine((file) => file.size > 0, "required"),
})

export async function addProduct(formData: FormData) {
	const result = addSchema.safeParse(Object.fromEntries(formData.entries()))

	if (!result.success) {
		return result.error
	}

	const data = result.data

	await fs.mkdir("products", { recursive: true })

	const filePath = `products/${crypto.randomUUID()}-${data.file.name}`

	await fs.writeFile(filePath, Buffer.from(await data.file.arrayBuffer()))

	await fs.mkdir("public/products", { recursive: true })

	const imagePath = `/products/${crypto.randomUUID()}-${data.image.name}`

	await fs.writeFile(`public/${imagePath}`, Buffer.from(await data.image.arrayBuffer()))

	await db.product.create({
		data: {
			name: data.name,
			priceInCents: data.priceInCents,
			description: data.description,
			filePath,
			imagePath,
		},
	})

    redirect("admin/products")
}

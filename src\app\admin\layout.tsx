import { Nav, NavLink } from "@/components/Nav";
import { ReactNode } from "react";

export const dynamic = "force-dynamic"

export default function Layout({ children }: { children: ReactNode }) {
  return <>
  <Nav>

    <NavLink href="/admin">Dashboard</NavLink>
    <NavLink href="/admin/products">Products</NavLink>
    <NavLink href="/admin/users">Users</NavLink>
    <NavLink href="/admin/orders">Orders</NavLink>
  </Nav>
  <div className="container p-6">
    {children}
  </div></>;
}
